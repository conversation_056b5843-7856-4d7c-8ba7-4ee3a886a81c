Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Oracle_SQL,Start: Create or replace package End:Object-1&Start: Create or replace editionable package End:Object-1&Start: Create package End:Object-1,Start:/* End:*/&Start:-- End:\n,,Package,ObjectTypes object (979)
Oracle_SQL,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (667)
Oracle_SQL,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (130)
Oracle_SQL,Start:create table End:object-1&Start:create global temporary table End:;&Start:create table End:;,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (685)
Oracle_SQL,Start:Create or replace synonym End:object-1&Start:Create or replace synonym End:;&Start:Create synonym End:object-1&Start:Create synonym End:;,,,Synonym,ObjectTypes object (655)
Oracle_SQL,Start: Create or replace type End:Object-1&Start: Create or replace type End:;,Start:/* End:*/&Start:-- End:\n,,Type,ObjectTypes object (670)
Oracle_SQL,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (637)
Oracle_SQL,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (673)
Oracle_SQL,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (658)
Oracle_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (676)
Oracle_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (643)
Oracle_SQL,Start:Create materialized view End:object-1&Start:Create materialized view End:;,,,Materialized_View,ObjectTypes object (661)
Oracle_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (682)
Oracle_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (679)
Oracle_SQL,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (646)
Oracle_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (649)
Oracle_SQL,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (133)
Oracle_SQL,Start: Create or replace procedure End:is|as&Start: Create procedure End:is|as&Start:as|is End:;&Start:;+1 End:;&Start:select End:;&Start: Create or replace editionable procedure End:is|as&Start: Create or alter editionable procedure End:is|as,Start: /* End: */&Start: -- End: \n,,Procedure/Statement,ObjectTypes object (145)
Oracle_SQL,Start: Create or replace procedure End:Object-1&Start: Create or replace  editionable procedure End:Object-1&Start: Create procedure End:Object-1&Start: Create or alter editionable procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (127)

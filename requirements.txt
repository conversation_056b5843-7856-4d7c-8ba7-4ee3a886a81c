# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.9
    # via langchain-community
aiosignal==1.3.2
    # via aiohttp
altair==5.5.0
    # via streamlit
annotated-types==0.7.0
    # via pydantic
anthropic==0.52.2
    # via langchain-anthropic
anyio==4.9.0
    # via
    #   anthropic
    #   groq
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
blinker==1.9.0
    # via streamlit
cachetools==5.5.2
    # via
    #   google-auth
    #   streamlit
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   streamlit
    #   uvicorn
colorama==0.4.6
    # via
    #   click
    #   tqdm
    #   uvicorn
dataclasses-json==0.6.7
    # via langchain-community
distro==1.9.0
    # via
    #   anthropic
    #   groq
    #   openai
et-xmlfile==2.0.0
    # via openpyxl
fastapi==0.115.12
    # via web-agents (pyproject.toml)
filetype==1.2.0
    # via langchain-google-genai
frozenlist==1.6.2
    # via
    #   aiohttp
    #   aiosignal
gitdb==4.0.12
    # via gitpython
gitpython==3.1.44
    # via streamlit
google-ai-generativelanguage==0.6.18
    # via langchain-google-genai
google-api-core==2.25.0
    # via google-ai-generativelanguage
google-auth==2.40.3
    # via
    #   google-ai-generativelanguage
    #   google-api-core
googleapis-common-protos==1.70.0
    # via
    #   google-api-core
    #   grpcio-status
greenlet==3.2.3
    # via sqlalchemy
groq==0.26.0
    # via langchain-groq
grpcio==1.72.1
    # via
    #   google-api-core
    #   grpcio-status
grpcio-status==1.72.1
    # via google-api-core
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   anthropic
    #   groq
    #   langgraph-sdk
    #   langsmith
    #   ollama
    #   openai
httpx-sse==0.4.0
    # via langchain-community
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
jinja2==3.1.6
    # via
    #   altair
    #   pydeck
jiter==0.10.0
    # via
    #   anthropic
    #   openai
joblib==1.5.1
    # via scikit-learn
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonschema==4.24.0
    # via altair
jsonschema-specifications==2025.4.1
    # via jsonschema
langchain==0.3.25
    # via langchain-community
langchain-anthropic==0.3.15
    # via web-agents (pyproject.toml)
langchain-community==0.3.24
    # via web-agents (pyproject.toml)
langchain-core==0.3.64
    # via
    #   langchain
    #   langchain-anthropic
    #   langchain-community
    #   langchain-google-genai
    #   langchain-groq
    #   langchain-ollama
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-google-genai==2.1.5
    # via web-agents (pyproject.toml)
langchain-groq==0.3.2
    # via web-agents (pyproject.toml)
langchain-ollama==0.3.3
    # via web-agents (pyproject.toml)
langchain-openai==0.3.19
    # via web-agents (pyproject.toml)
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.8
    # via web-agents (pyproject.toml)
langgraph-checkpoint==2.0.26
    # via
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.45
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
markupsafe==3.0.2
    # via jinja2
marshmallow==3.26.1
    # via dataclasses-json
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via typing-inspect
narwhals==1.41.0
    # via altair
numpy==2.2.6
    # via
    #   langchain-community
    #   pandas
    #   pydeck
    #   scikit-learn
    #   scipy
    #   streamlit
ollama==0.5.1
    # via langchain-ollama
openai==1.84.0
    # via langchain-openai
openpyxl==3.1.5
    # via web-agents (pyproject.toml)
orjson==3.10.18
    # via
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==24.2
    # via
    #   altair
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   streamlit
pandas==2.3.0
    # via
    #   web-agents (pyproject.toml)
    #   streamlit
pillow==11.2.1
    # via
    #   web-agents (pyproject.toml)
    #   streamlit
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==6.31.1
    # via
    #   google-ai-generativelanguage
    #   google-api-core
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
    #   streamlit
psycopg2-binary==2.9.10
    # via web-agents (pyproject.toml)
pyarrow==20.0.0
    # via streamlit
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pydantic==2.11.5
    # via
    #   web-agents (pyproject.toml)
    #   anthropic
    #   fastapi
    #   groq
    #   langchain
    #   langchain-anthropic
    #   langchain-core
    #   langchain-google-genai
    #   langgraph
    #   langsmith
    #   ollama
    #   openai
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via langchain-community
pydeck==0.9.1
    # via streamlit
pyjwt==2.10.1
    # via web-agents (pyproject.toml)
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.0
    # via
    #   web-agents (pyproject.toml)
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via web-agents (pyproject.toml)
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   langchain
    #   langchain-community
    #   langchain-core
    #   uvicorn
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via tiktoken
requests==2.32.3
    # via
    #   google-api-core
    #   langchain
    #   langchain-community
    #   langsmith
    #   requests-toolbelt
    #   streamlit
    #   tiktoken
requests-toolbelt==1.0.0
    # via langsmith
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
scikit-learn==1.7.0
    # via web-agents (pyproject.toml)
scipy==1.15.3
    # via scikit-learn
six==1.17.0
    # via python-dateutil
smmap==5.0.2
    # via gitdb
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   groq
    #   openai
sqlalchemy==2.0.41
    # via
    #   langchain
    #   langchain-community
starlette==0.46.2
    # via fastapi
streamlit==1.45.1
    # via web-agents (pyproject.toml)
tenacity==9.1.2
    # via
    #   langchain-community
    #   langchain-core
    #   streamlit
threadpoolctl==3.6.0
    # via scikit-learn
tiktoken==0.9.0
    # via langchain-openai
toml==0.10.2
    # via streamlit
tornado==6.5.1
    # via streamlit
tqdm==4.67.1
    # via openai
typing-extensions==4.14.0
    # via
    #   altair
    #   anthropic
    #   anyio
    #   fastapi
    #   groq
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   referencing
    #   sqlalchemy
    #   streamlit
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via requests
uvicorn==0.34.3
    # via web-agents (pyproject.toml)
watchdog==6.0.0
    # via streamlit
watchfiles==1.0.5
    # via uvicorn
websockets==15.0.1
    # via uvicorn
xxhash==3.5.0
    # via langgraph
yarl==1.20.0
    # via aiohttp
zstandard==0.23.0
    # via langsmith
cryptography
pycryptodome
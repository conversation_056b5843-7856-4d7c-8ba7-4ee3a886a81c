Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Oracle_Oracle,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (787)
Oracle_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (814)
Oracle_Oracle,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (844)
Oracle_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (853)
Oracle_Oracle,Start: Create or replace type End:Object-1&Start: Create or replace type End:;,Start:/* End:*/&Start:-- End:\n,,Type,ObjectTypes object (838)
Oracle_Oracle,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (790)
Oracle_Oracle,Start: Create or replace package End:Object-1&Start: Create or replace editionable package End:Object-1&Start: Create package End:Object-1,Start:/* End:*/&Start:-- End:\n,,Package,ObjectTypes object (817)
Oracle_Oracle,Start:create table End:object-1&Start:create global temporary table End:;&Start:create table End:;,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (856)
Oracle_Oracle,Start:create table End:object-1&Start:create table End:;,,,Partition,ObjectTypes object (802)
Oracle_Oracle,Start:Create or replace synonym End:object-1&Start:Create or replace synonym End:;&Start:Create synonym End:object-1&Start:Create synonym End:;,,,Synonym,ObjectTypes object (826)
Oracle_Oracle,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (841)
Oracle_Oracle,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (829)
Oracle_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (847)
Oracle_Oracle,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (781)
Oracle_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (805)
Oracle_Oracle,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (784)
Oracle_Oracle,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (808)
Oracle_Oracle,Start:Create materialized view End:object-1&Start:Create materialized view End:;,,,Materialized_View,ObjectTypes object (832)
Oracle_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (850)

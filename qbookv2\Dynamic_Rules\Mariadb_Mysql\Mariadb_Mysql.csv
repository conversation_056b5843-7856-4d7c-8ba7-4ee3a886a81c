Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Mariadb_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (307)
Mariadb_Mysql,Start:Create materialized view End:object-1&Start:Create materialized view End:;,,,Materialized_View,ObjectTypes object (328)
Mariadb_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (346)
Mariadb_Mysql,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1&Start: Create Definer End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (280)
Mariadb_Mysql,Start: Create or replace package End:Object-1&Start: Create or replace editionable package End:Object-1&Start: Create package End:Object-1,Start:/* End:*/&Start:-- End:\n,,Package,ObjectTypes object (310)
Mariadb_Mysql,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (334)
Mariadb_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (349)
Mariadb_Mysql,"Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;&Start: Create view End:;
",Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (283)
Mariadb_Mysql,Start:create table End:object-1&Start:create table End:;,,,Partition,ObjectTypes object (295)
Mariadb_Mysql,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (274)
Mariadb_Mysql,Start:Select End:;&Start:insert End:;,Start:/* End:*/&Start:-- End:\n,,Query,ObjectTypes object (319)
Mariadb_Mysql,Start: Create or replace type End:Object-1&Start: Create or replace type End:;,Start:/* End:*/&Start:-- End:\n,,Type,ObjectTypes object (337)
Mariadb_Mysql,Start:create table End:object-1&Start:create global temporary table End:;&Start:create table End:;,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (352)
Mariadb_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (298)
Mariadb_Mysql,Start:Create or replace synonym End:object-1&Start:Create or replace synonym End:;&Start:Create synonym End:object-1&Start:Create synonym End:;,,,Synonym,ObjectTypes object (322)
Mariadb_Mysql,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (340)
Mariadb_Mysql,Start: Create Event End:Object-1,Start:/* End:*/&Start:-- End:\n,,Event,ObjectTypes object (355)
Mariadb_Mysql,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1&Start: Create Definer End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (277)
Mariadb_Mysql,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (301)
Mariadb_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (343)
Mariadb_Mysql,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (325)

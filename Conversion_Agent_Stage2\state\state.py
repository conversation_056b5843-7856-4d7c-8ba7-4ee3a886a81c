"""
Stage 2 Workflow State Management for QMigrator Module Updates.

This module defines the state management for Stage 2 processing, which handles
AI corrections from Stage 1 to update QMigrator Python modules through a
comprehensive 15-step workflow with retry mechanism.
"""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field


class Stage2WorkflowState(BaseModel):
    """
    Comprehensive workflow state for Stage 2 QMigrator module update processing.

    Implements the complete 15-step workflow as per Stage2_LangGraph_Workflow.md
    """

    # Core input parameters
    migration_name: str = Field(description="Migration name (e.g., Oracle_Postgres14)")
    process_type: Literal["qmigrator", "qbook"] = Field(description="Process type: qmigrator (object-level) or qbook (statement-level)")
    schema_name: str = Field(description="Schema name for the object")
    object_name: str = Field(description="Name of the object being processed")
    cloud_category: str = Field(description="Cloud category: 'local' or 'cloud'")

    # Process type specific fields
    objecttype: Optional[str] = Field(default=None, description="Type of the object (required for qmigrator)")
    source_statement: Optional[str] = Field(default=None, description="Individual source statement (required for qbook)")
    converted_statement: Optional[str] = Field(default=None, description="Individual converted statement (required for qbook)")

    # Optional fields
    target_object_id: Optional[int] = Field(default=None, description="Target object ID from Stage 1")

    # Source and target code
    source_code: Optional[str] = Field(default=None, description="Complete source code")
    target_code: Optional[str] = Field(default=None, description="Complete target code")

    # Processing results
    approved_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="List of approved statements from Stage 1")
    object_level_features: Optional[List[Dict[str, Any]]] = Field(default=None, description="Object-level feature analysis results")
    available_features_with_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="Combined approved statements with features")

    # Current processing state
    current_statement_index: int = Field(default=0, description="Index of current statement being processed")
    current_statement: Optional[Dict[str, Any]] = Field(default=None, description="Current statement being processed")

    # Feature identification
    responsible_features: Optional[List[str]] = Field(default=None, description="Responsible statement-level features")
    responsible_procedures: Optional[List[str]] = Field(default=None, description="Responsible program-level features")

    # Module updates
    updated_modules: Optional[List[Dict[str, Any]]] = Field(default=None, description="Updated Python modules")
    module_test_result: Optional[bool] = Field(default=None, description="Module functionality test result")
    updated_ai_converted_statement: Optional[str] = Field(default=None, description="Statement after applying updated modules")

    # Attempt tracking
    current_attempt: int = Field(default=1, description="Current attempt number (max 5)")
    max_attempts: int = Field(default=5, description="Maximum attempts per statement")

    # Database tracking
    current_statement_id: Optional[int] = Field(default=None, description="Database ID of current statement")

    # Workflow control flags
    features_validation_passed: bool = Field(default=False, description="Available features validation passed")
    responsible_features_valid: bool = Field(default=False, description="Responsible features validation passed")
    code_quality_valid: bool = Field(default=False, description="Code quality validation passed")
    ai_statements_match: bool = Field(default=False, description="AI statements comparison passed")

    # Final results
    completed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Successfully processed statements")
    failed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Statements that failed after max attempts")
    workflow_completed: bool = Field(default=False, description="Entire workflow completed")
# Standard library imports
import os
import pandas as pd
from typing import Dict, Any

# Local imports - State
from Conversion_Agent_Stage2.state import Stage2WorkflowState
from config import Config
from Conversion_Agent_Stage2.qmigrator_conversion.object_conversion import qbook_object_conversion


class Stage2ProcessingNodes:
    """
    Stage 2 processing nodes for QMigrator module updates.

    This class provides all 15 workflow nodes for the complete Stage 2 processing
    as defined in Stage2_LangGraph_Workflow.md. Handles both qmigrator (object-level)
    and qbook (statement-level) processing with comprehensive retry mechanisms.

    Complete Workflow Nodes:
        1. Process Type Decision
        2. Post Stage1 Processing (QMigrator)
        3. Statement Level Processing (QBook)
        4. Map Feature Combinations (QMigrator only)
        5. Available Features Validation (QMigrator only)
        6. Identify Responsible Features
        7. Features Valid Decision
        8. Update Responsible Modules
        9. Code Quality Valid Decision
        10. Apply Updated Modules
        11. Test Modules
        12. Compare AI Statements
        13. More Statements Decision
        14. Complete Processing

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 processing nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
        """
        self.llm = llm

    # ==================== WORKFLOW DECISION NODES ====================

    def process_type_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 1: Process Type Decision Node.

        Routes workflow based on process_type parameter.
        - qmigrator: Routes to QMigrator-specific file processing
        - qbook: Routes to QBook-specific statement processing
        """
        print(f"🔀 Process Type Decision: {state.process_type}")

        if state.process_type == "qmigrator":
            print("📁 Routing to QMigrator object-level processing")
        else:
            print("📝 Routing to QBook statement-level processing")

        # Return state update - routing is handled by conditional edges
        return {
            "process_type": state.process_type
        }

    # ==================== QMIGRATOR PATH NODES ====================

    def post_stage1_processing_qmigrator(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Post-Stage 1 Processing Node (QMigrator Path).

        Purpose: Read Stage 1 approved statements and source code, run QMigrator conversion.

        Process:
            1. Read approved_statements.csv from Stage 1 metadata
            2. Read source_code.sql file
            3. Run QMigrator object-level conversion
            4. Return DataFrames for workflow processing

        Args:
            state: Stage2WorkflowState containing migration context

        Returns:
            Dict containing DataFrames and conversion results
        """
        print("🔄 Starting Post-Stage 1 Processing (QMigrator)...")
        print(f"🔧 Migration: {state.migration_name}")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}, Type: {state.objecttype}")

        try:
            # 1. Determine QBook path based on cloud_category
            if state.cloud_category.lower() == "local":
                qbook_root_path = Config.Qbook_Local_Path
                print(f"🏠 Using QBook Local path: {qbook_root_path}")
            else:  # Cloud
                qbook_root_path = Config.Qbook_Path
                print(f"☁️ Using QBook Cloud path: {qbook_root_path}")

            # 2. Construct file paths
            metadata_dir = os.path.join(
                qbook_root_path,
                'Stage1_Metadata',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )

            approved_statements_path = os.path.join(metadata_dir, 'approved_statements.csv')
            source_code_path = os.path.join(metadata_dir, 'source_code.sql')

            print(f"📁 Metadata directory: {metadata_dir}")

            # 3. Read approved statements CSV
            if not os.path.exists(approved_statements_path):
                raise FileNotFoundError(f"Approved statements file not found: {approved_statements_path}")

            approved_statements_df = pd.read_csv(approved_statements_path)
            print(f"📊 Loaded {len(approved_statements_df)} approved statements")

            # 4. Read source code file
            if not os.path.exists(source_code_path):
                raise FileNotFoundError(f"Source code file not found: {source_code_path}")

            with open(source_code_path, 'r', encoding='utf-8') as f:
                source_code_content = f.read()

            if not source_code_content.strip():
                raise ValueError("Source code file is empty")

            print(f"📝 Loaded source code ({len(source_code_content)} characters)")

            # 5. Run QMigrator object-level conversion
            print("🔄 Running QMigrator object-level conversion...")

            object_converted_output, available_features_df = qbook_object_conversion(
                migration_name=state.migration_name,
                schema_name=state.schema_name,
                object_type=state.objecttype,
                object_name=state.object_name,
                source_data=source_code_content,
                cloud_category=state.cloud_category
            )

            if object_converted_output is None:
                raise ValueError("QMigrator conversion failed - no output generated")

            print(f"✅ QMigrator conversion completed")
            print(f"📊 Available features: {len(available_features_df)} statements")

            # 6. Update workflow state for persistence
            state.approved_statements = approved_statements_df.to_dict('records')
            state.object_level_features = available_features_df.to_dict('records')
            state.source_code = source_code_content

            print("✅ Post-Stage 1 Processing (QMigrator) completed successfully")
            print(f"📊 Total approved statements: {len(approved_statements_df)}")
            print(f"📊 Total available features: {len(available_features_df)}")

            # 7. Return DataFrames for immediate use by next node
            return {
                "approved_statements_df": approved_statements_df,
                "available_features_df": available_features_df,
                "source_code": source_code_content,
                "object_converted_output": object_converted_output
            }

        except FileNotFoundError as e:
            error_msg = f"❌ File not found: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "source_code": "",
                "object_converted_output": ""
            }

        except Exception as e:
            error_msg = f"❌ Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "source_code": "",
                "object_converted_output": ""
            }

    def map_feature_combinations(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 3: Map Feature Combinations Node (QMigrator Only).

        Purpose: Map approved statements with object-level features using AI matching.

        Process:
            1. Get approved statements and available features from workflow state
            2. Use AI to map statements with their corresponding features
            3. Create combined dataset for feature analysis

        Args:
            state: Stage2WorkflowState containing approved_statements and object_level_features

        Returns:
            Dict containing available_features_with_statements
        """
        print("🔄 Starting Map Feature Combinations...")

        try:
            import pandas as pd

            # Get DataFrames from workflow state
            if not state.approved_statements or not state.object_level_features:
                raise ValueError("Missing approved statements or object level features from previous step")

            # Convert state data back to DataFrames for processing
            approved_statements_df = pd.DataFrame(state.approved_statements)
            available_features_df = pd.DataFrame(state.object_level_features)

            print(f"📊 Processing {len(approved_statements_df)} approved statements")
            print(f"📊 Processing {len(available_features_df)} available features")

            # TODO: Implement AI mapping logic
            # For now, create a simple mapping based on statement numbers
            # This will be enhanced with AI logic in future iterations

            combined_features = []

            for _, approved_stmt in approved_statements_df.iterrows():
                # Find matching features by statement number if available
                matching_features = available_features_df[
                    available_features_df['Statement_Number'] == approved_stmt.get('source_statement_number', 0)
                ]

                if not matching_features.empty:
                    feature_row = matching_features.iloc[0]
                    combined_entry = {
                        # From approved statements
                        "statement_id": len(combined_features) + 1,
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # From available features
                        "statement_after_typecasting": feature_row.get('Statement_After_Typecasting', ''),
                        "statement_level_output": feature_row.get('Statement_Level_Output', ''),
                        "available_features": feature_row.get('Available_Features', []),
                        "post_features": feature_row.get('Post_Features', [])
                    }
                else:
                    # No matching features found, create entry with approved statement only
                    combined_entry = {
                        "statement_id": len(combined_features) + 1,
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # Empty features
                        "statement_after_typecasting": '',
                        "statement_level_output": '',
                        "available_features": [],
                        "post_features": []
                    }

                combined_features.append(combined_entry)

            # Update workflow state
            state.available_features_with_statements = combined_features

            print("✅ Map Feature Combinations completed successfully")
            print(f"📊 Total mapped combinations: {len(combined_features)}")

            return {
                "available_features_with_statements": combined_features
            }

        except Exception as e:
            error_msg = f"❌ Map Feature Combinations failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "available_features_with_statements": []
            }

    def available_features_validation(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 4: Available Features Validation Node (QMigrator Only).

        Purpose: Validate feature identification accuracy between approved statements and features.

        Returns:
            Dict with features_validation_passed flag
        """
        print("🔄 Starting Available Features Validation...")

        # TODO: Implement AI validation of feature mapping accuracy
        # - Validate feature identification accuracy
        # - Cross-reference mapping correctness
        # - Ensure features correctly correspond to statement-level issues

        print("✅ Available Features Validation completed successfully")

        return {
            "features_validation_passed": True  # Placeholder
        }

    # ==================== QBOOK PATH NODES ====================

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            1. Run QMigrator statement-level conversion on single statement
            2. Extract statement-level features
            3. Store metadata for single statement processing
        """
        print("🔄 Starting Statement Level Processing (QBook)...")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}")
        print(f"📝 Processing single source statement (length: {len(state.source_statement) if state.source_statement else 0})")
        print(f"📝 Processing single converted statement (length: {len(state.converted_statement) if state.converted_statement else 0})")

        # Validate that we have the required single statement data
        if not state.source_statement or not state.converted_statement:
            raise ValueError("QBook processing requires both source_statement and converted_statement")

        # TODO: Implement QBook statement-level processing logic
        # - Run QMigrator statement-level conversion on the single statement
        # - Extract features for the individual statement
        # - Store metadata for single statement
        # - Return statement-level features and analysis

        print("✅ Statement Level Processing (QBook) completed successfully")
        print("📊 Processed 1 statement (single statement mode)")

        # Return single statement processing results
        return {
            "approved_statements": [{
                "source_statement": state.source_statement,
                "converted_statement": state.converted_statement,
                "schema_name": state.schema_name,
                "object_name": state.object_name
            }],
            "statement_count": 1,
            "processing_mode": "single_statement"
        }

    # ==================== COMMON PROCESSING NODES ====================

    def identify_responsible_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 5: Identify Responsible Features Node.

        Purpose: Identify specific modules causing original_deployment_error.

        Process:
            1. Analyze original_error against source vs ai_converted statements
            2. From available features, identify subset causing conversion issues
            3. Separate statement-level and program-level responsible features
        """
        print("🔄 Starting Identify Responsible Features...")

        if state.current_statement:
            print(f"📝 Processing statement {state.current_statement_index + 1}")

        # TODO: Implement AI analysis to identify responsible features
        # - Analyze original_error against statements
        # - Identify subset of features causing issues
        # - Separate statement-level and program-level features

        print("✅ Identify Responsible Features completed successfully")

        return {
            "responsible_features": [],
            "responsible_procedures": []
        }

    def features_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 6: Features Valid Decision Node.

        Purpose: Verify responsible features actually cause the identified issues.

        Process:
            1. AI validation of responsible features
            2. Insert into stage2_statements table if valid
            3. Update with responsible features
        """
        print("🔄 Starting Features Valid Decision...")

        # TODO: Implement AI validation of responsible features
        # - Verify features actually cause identified issues
        # - Insert into stage2_statements table
        # - Update with responsible features

        print("✅ Features Valid Decision completed successfully")

        return {
            "responsible_features_valid": True,  # Placeholder
            "current_statement_id": 1  # Placeholder database ID
        }

    def update_responsible_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 7: Update Responsible Modules Node.

        Purpose: Read and update Python modules for responsible features.

        Process:
            1. Read CSV file for feature path resolution
            2. Construct module paths and read Python files
            3. AI module update based on conversion requirements
            4. Generate modified code for each responsible module
        """
        print("🔄 Starting Update Responsible Modules...")
        print(f"🔧 Attempt {state.current_attempt} of {state.max_attempts}")

        # TODO: Implement module update logic
        # - Read CSV file from Conversion_Modules/{migration_name}/{migration_name}.csv
        # - Lookup Object_Path for each responsible feature
        # - Construct full paths and read Python files
        # - AI module update based on conversion requirements
        # - Generate modified code

        print("✅ Update Responsible Modules completed successfully")

        return {
            "updated_modules": []
        }

    def code_quality_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 8: Code Quality Valid Decision Node.

        Purpose: Validate modified Python modules for syntax, logic, and conversion requirements.

        Process:
            1. AI code validation of modified modules
            2. Insert into stage2_conversion_modules table if valid
            3. Store module IDs for future updates
        """
        print("🔄 Starting Code Quality Valid Decision...")

        # TODO: Implement AI code validation
        # - Validate modified Python modules
        # - Check syntax, logic, and conversion requirements
        # - Insert into stage2_conversion_modules table

        print("✅ Code Quality Valid Decision completed successfully")

        return {
            "code_quality_valid": True  # Placeholder
        }

    def apply_updated_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 9: Apply Updated Modules Node.

        Purpose: Apply updated modules to generate new converted statement.

        Process:
            1. Start with after_type_casting_statement
            2. Apply statement-level features (updated + original modules)
            3. Apply post-procedure features
            4. Generate updated_AI_Converted_Statement
        """
        print("🔄 Starting Apply Updated Modules...")

        # TODO: Implement module application logic
        # - Start with after_type_casting_statement
        # - Apply all available feature modules
        # - Use modified code for updated modules
        # - Use original modules for non-updated features
        # - Generate updated_AI_Converted_Statement

        print("✅ Apply Updated Modules completed successfully")

        return {
            "updated_ai_converted_statement": ""  # Placeholder
        }

    def test_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 10: Test Modules Node.

        Purpose: Test updated modules in QMigrator environment.

        Process:
            1. Module functionality test
            2. Verify modules execute without errors
            3. Validate conversion logic works correctly
        """
        print("🔄 Starting Test Modules...")

        # TODO: Implement module testing logic
        # - Test updated modules in QMigrator environment
        # - Verify modules execute without errors
        # - Validate conversion logic

        print("✅ Test Modules completed successfully")

        return {
            "module_test_result": True  # Placeholder
        }

    def compare_ai_statements(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 11: Compare AI Statements Node.

        Purpose: Compare original AI converted statement with updated statement.

        Process:
            1. Compare ai_converted_statement vs updated_AI_Converted_Statement
            2. AI determines functional equivalence
            3. Account for acceptable variations in formatting/syntax
        """
        print("🔄 Starting Compare AI Statements...")

        # TODO: Implement AI statement comparison
        # - Compare original vs updated AI converted statements
        # - Determine functional equivalence
        # - Account for formatting/syntax variations

        print("✅ Compare AI Statements completed successfully")

        return {
            "ai_statements_match": True  # Placeholder
        }

    def more_statements_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 12: More Statements Decision Node.

        Purpose: Check if more statements exist in the approved statements list.

        Process:
            1. Check if more statements exist
            2. Update current_statement_index
            3. Set current_statement for next iteration
        """
        print("🔄 Starting More Statements Decision...")

        # TODO: Implement statement iteration logic
        # - Check if more statements exist
        # - Update current_statement_index
        # - Set current_statement for next iteration
        # - Reset attempt counter for new statement

        print("✅ More Statements Decision completed successfully")

        return {
            "current_statement_index": state.current_statement_index + 1,
            "current_attempt": 1  # Reset for new statement
        }

    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize workflow and generate processing summary.

        Process:
            1. Update stage2_conversion_modules for successful modules
            2. Generate processing summary report
            3. Clean up temporary resources
            4. Mark workflow as completed
        """
        print("🔄 Starting Complete Processing...")

        # TODO: Implement completion logic
        # - Update stage2_conversion_modules for successful modules
        # - Generate processing summary report
        # - Clean up temporary resources
        # - Mark workflow as completed

        print("✅ Complete Processing completed successfully")
        print("🎉 Stage 2 workflow completed!")

        return {
            "workflow_completed": True
        }

    # ==================== DECISION HELPER FUNCTIONS ====================

    def should_retry_features_validation(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if features validation should be retried."""
        return not state.features_validation_passed

    def should_retry_responsible_features(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if responsible features identification should be retried."""
        return not state.responsible_features_valid

    def should_retry_code_quality(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if code quality validation should be retried."""
        return not state.code_quality_valid

    def should_retry_module_test(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if module testing should be retried."""
        return not state.module_test_result

    def should_retry_ai_comparison(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if AI comparison should be retried."""
        return not state.ai_statements_match and state.current_attempt < state.max_attempts

    def has_more_statements(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if more statements need processing."""
        if not state.approved_statements:
            return False
        return state.current_statement_index < len(state.approved_statements) - 1

    def should_increment_attempt(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if attempt counter should be incremented."""
        return not state.ai_statements_match and state.current_attempt < state.max_attempts
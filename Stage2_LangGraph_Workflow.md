# Stage 2 LangGraph Workflow Documentation

## Overview
Stage 2 LangGraph workflow processes AI-corrected statements from Stage 1 through a comprehensive 15-step validation and module update process. The workflow handles both object-level (qmigrator) and statement-level (qbook) processing with intelligent AI validation loops and retry mechanisms. The workflow stores data for a separate manual review interface that manages conversion modules and attempts.

## Workflow Diagram

```mermaid
flowchart TD
    A[Start Stage2 Workflow] --> B{Process Type}

    B -->|qmigrator| C[Post Stage1 Processing - QMigrator]
    B -->|qbook| D[Statement Level Processing - QBook]

    C --> E[Map Feature Combinations]
    D --> F[Identify Responsible Features]

    E --> G[Available Features Validation]
    G -->|Failed| E
    G -->|Success| F

    F --> H[Features Valid?]
    H -->|Failed| F
    H -->|Success| I[Update Responsible Modules]

    I --> J[Code Quality Valid?]
    J -->|Failed| I
    J -->|Success| K[Apply Updated Modules]

    K --> L[Test Modules]
    L -->|Failed| I
    L -->|Success| M[Compare AI Statements]

    M -->|Match| N{More Statements?}
    M -->|No Match & Max Attempts| N
    M -->|No Match & Retry| I

    N -->|Yes| F
    N -->|No| O[Complete Processing]

    %% Styling
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef qmigratorOnly fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef processing fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px,color:#000
    classDef validation fill:#fff8e1,stroke:#f57f17,stroke-width:2px,color:#000
    classDef moduleWork fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000

    class A,O startEnd
    class B,H,J,L,N decision
    class E,G qmigratorOnly
    class C,D,F,K processing
    class M validation
    class I moduleWork
```

## Stage 1 to Stage 2 Transition

### Stage 1 Metadata Generation
Stage 1 generates metadata files that Stage 2 workflow consumes:

#### Directory Structure:
```
{Config.Qbook_Local_Path or Config.Qbook_Path}/Stage1_Metadata/
├── {migration_name}/
    ├── {schema_name}/
    │   ├── Conversion_Files.xlsx                    ← Schema level (copied from PRJ{project_id}SRC/{run_no}/Source/{schema_name}/)
    │   ├── *_Package_Extraction.csv                 ← Schema level (all files ending with _Package_Extraction.csv)
    │   └── {object_type}/
    │       └── {object_name}/
    │           ├── approved_statements.csv          ← Object level
    │           ├── source_code.sql
    │           ├── target_code.sql
    │           └── final_code.sql
```

#### File Contents:
- **approved_statements.csv**: Contains approved statements from Stage 1 with columns:
  - `migration_name`, `schema_name`, `object_name`, `object_type`, `tgt_object_id`
  - `source_statement_number`, `target_statement_number`
  - `original_source_statement`, `original_target_statement`, `ai_converted_statement`
  - `original_deployment_error`
- **source_code.sql**: Complete original source code
- **target_code.sql**: Complete target code
- **final_code.sql**: Final reviewed and approved code
- **Conversion_Files.xlsx**: Project-specific conversion files from fileshare
- **Pattern-based files**: All files ending with `_Package_Extraction.csv` (e.g., HR_Package_Extraction.csv, FINANCE_Package_Extraction.csv)

#### File Copying Strategy:
- **Single File Copy**: `copy_project_file()` function for specific files like `Conversion_Files.xlsx`
- **Pattern-Based Copy**: `copy_files_by_pattern()` function for files ending with specific patterns
- **Graceful Handling**: No errors raised if files don't exist - informational logging only
- **Multiple File Support**: Pattern matching can copy multiple files (e.g., HR_Package_Extraction.csv, FINANCE_Package_Extraction.csv)

#### Path Configuration:
- **Local Environment**: Uses `Config.Qbook_Local_Path` for QBook files, `Config.Local_Path` for project files
- **Cloud Environment**: Uses `Config.Qbook_Path` for QBook files, `Config.Cloud_Path` for project files
- **Dynamic Project ID**: Uses `PRJ{project_id}SRC` format for fileshare paths
- **Source Path**: `PRJ{project_id}SRC/{run_no}/Source/{schema_name}/`
- **Target Path**: `Stage1_Metadata/{migration_name}/{schema_name}/`

## Workflow Steps

### 1. Start Stage2 Workflow
- **Entry Point**: Initialize Stage 2 LangGraph workflow
- **Purpose**: Set up workflow context and prepare for process type routing
- **Actions**:
  - Initialize workflow state
  - Validate input parameters
  - Prepare workflow context for subsequent processing

### 2. Process Type Decision
- **Decision Logic**: Routes workflow based on `process_type` parameter
- **Input Parameter**: `process_type` ("qmigrator" or "qbook")
- **QMigrator Path**: Routes to QMigrator-specific file processing
- **QBook Path**: Routes to QBook-specific statement processing

### 3. Post Stage1 Processing - QMigrator

#### For QMigrator Process Type:
- **Input**: `process_type="qmigrator"`, `migration_name`, `schema_name`, `object_name`, `objecttype`
- **File Reading**:
  - Navigate to `Stage1_Metadata/migration_name/schema_name/objecttype/object_name` folder in QBook path (as per config.py)
  - Read `approved_statements.csv` file containing approved statements with columns:
    - `migration_name`, `schema_name`, `object_name`, `object_type`, `tgt_object_id`, `source_statement_number`, `target_statement_number`, `original_source_statement`, `original_target_statement`, `ai_converted_statement`, `original_deployment_error`
  - Read `source_code.sql` file for complete source code
  - Read `target_code.sql` file for complete target code
  - Read `final_code.sql` file for final reviewed code
  - Access `Conversion_Files.xlsx` from `Stage1_Metadata/migration_name/schema_name/` level
  - Access all `*_Package_Extraction.csv` files from `Stage1_Metadata/migration_name/schema_name/` level
- **QMigrator Object Level Conversion**:
  - **Pass**: `process_type`, `migration_name`, `schema_name`, `object_name`, `approved_statements`, `source_code`, `target_code`
  - **Returns**: Object-level analysis with features for all statements
  - **Output format**: 
    ```
    (tgt_object_id, source_statement_number, original_source_statement, after_type_casting_statement, after_statement_level, 
     [('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre'), ...], 
     [('procedure/post/post_procedure'), ('common/post/post_procedure'), ...])
    ```

### 4. Statement Level Processing - QBook

#### For QBook Process Type:
- **Input**: `process_type="qbook"`, `migration_name`, `schema_name`, `object_name`, `source_statement`, `converted_statement`
- **Direct Statement Processing**: No file reading required - statements provided directly in request
- **QMigrator Statement Level Conversion**:
  - **Pass**: `process_type`, `migration_name`, `schema_name`, `object_name`, `source_statement`, `converted_statement`
  - **Returns**: Statement-level analysis for single statement
  - **Output format**:
    ```
    (tgt_object_id, source_statement_number, original_source_statement, after_type_casting_statement, after_statement_level,
     [('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre'), ...],
     [('procedure/post/post_procedure'), ('common/post/post_procedure'), ...])
    ```

### 5. Map Feature Combinations (QMigrator Only)

#### Enhanced AI Mapping Process:
- **Challenge**: Map 5 approved statements with 50 object-level output statements
- **AI Matching Logic**:
  - Compare `(source_statement_number, source_statement)` from approved statements
  - Match with `(source_statement_number, original_source_statement)` from object-level output
  - **AI validates**: Are these statements functionally the same?
  - **Statement numbers may vary** but content should be nearly identical
  - **AI identifies exact matches** between approved and object-level statements

#### Mapping Process:
```
For each approved statement:
  1. AI compares with all 50 object-level statements
  2. Identifies the best matching original_source_statement
  3. Combines approved statement data with object-level features
  4. Forms combined output format
```

#### Output Format:
```
List of Available Features with Statements:
[
  (tgt_object_id, source_statement_number, original_source_statement, after_type_casting_statement, after_statement_level, 
   target_statement, ai_converted_statement, original_error, 
   [('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre'), ...], 
   [('procedure/post/post_procedure'), ('common/post/post_procedure'), ...]),
  (tgt_object_id, source_statement_number, original_source_statement, after_type_casting_statement, after_statement_level, 
   target_statement, ai_converted_statement, original_error, 
   [('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre'), ...], 
   [('procedure/post/post_procedure'), ('common/post/post_procedure'), ...])
]
```

#### Validation Rule:
- **Length validation**: `len(approved_statements) == len(Available_Features_with_Statements)`
- **Source priority**: Use `source_statement` from approved statements when similar

### 6. Available Features Validation (QMigrator Only)
- **AI Validation Process**:
  - Validate feature identification accuracy between approved statements and split source code
  - Cross-reference mapping correctness
  - Ensure features correctly correspond to statement-level issues
- **Success**: Proceed to responsible feature identification
- **Failure**: Return to Map Feature Combinations for re-mapping

### 7. Identify Responsible Features

#### Enhanced AI Analysis:
- **Purpose**: Identify specific modules causing `original_deployment_error`
- **AI Process**:
  - Analyze `original_error` against `original_source_statement` vs `ai_converted_statement`
  - From available features: `[('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre')]`
  - From available procedures: `[('procedure/post/post_procedure'), ('common/post/post_procedure')]`
  - **Identify subset causing conversion issues**

#### Output Format:
```
Responsible Statements:
[
  (tgt_object_id, source_statement_number, original_source_statement, after_type_casting_statement, after_statement_level, 
   target_statement, ai_converted_statement, original_error, 
   [('xml','procedure/statement/pre'), ('nvl', 'common/statement/pre')], // All features
   [('procedure/post/post_procedure'), ('common/post/post_procedure')], // All procedures
   [('xml','procedure/statement/pre')], // Responsible features only
   [('procedure/post/post_procedure')] // Responsible procedures only
  )
]
```

### 8. Features Valid Decision
- **AI Validation**: Verify responsible features actually cause the identified issues
- **Success Actions**:
  - **Insert into `stage2_statements`** with all statement data:
    ```sql
    INSERT INTO stage2_statements (
      statement_id, process_type, tgt_object_id, schema_name, tgt_object_name, 
      migration_name, original_error, original_source_statement, 
      source_before_statement_level, source_after_statement_level, 
      target_statement, ai_converted_statement, statement_level_features, 
      program_level_features, created_dt
    )
    ```
  - **Update with responsible features**:
    ```sql
    UPDATE stage2_statements SET 
      responsible_statement_level_features = ?, 
      responsible_program_level_features = ?, 
      updated_dt = NOW() 
    WHERE statement_id = ?
    ```
- **Failure**: Return to Identify Responsible Features

### 9. Update Responsible Modules

#### Enhanced Module Reading:
- **Conversion_Modules Folder Structure**:
  - Python modules are available in `Conversion_Modules` folder
  - Folder structure: `Conversion_Modules/{migration_name}/{object_path}/{feature_name}.py`
  - Feature object paths are available in `Conversion_Modules/{migration_name}/{migration_name}.csv` file
  - Each feature has corresponding `.py` files in specific object path folders
  - Example migration: `Oracle_Postgres14`
  - Example structure: `Conversion_Modules/Oracle_Postgres14/Common/Statement/Pre/nvl.py`

#### Feature Path Resolution:
- **CSV File Location**: `Conversion_Modules/{migration_name}/{migration_name}.csv`
- **CSV Content**: Contains migration details, feature names, and their corresponding object paths
- **Example CSV**: `Conversion_Modules/Oracle_Postgres14/Oracle_Postgres14.csv`
- **CSV Columns**: `Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path`
- **Sample CSV Data**:
  ```csv
  Migration_Name,Feature_Name,Keywords,Predecessor,Estimations,Automation_Percentage,Object_Path
  Oracle_Postgres14,Xmltype,xmltype,No Predecessor,2,100,Common/Statement/Pre
  Oracle_Postgres14,Nvl2,nvl2*,No Predecessor,2,70,Common/Statement/Pre
  Oracle_Postgres14,Cursor,cursor & is,Post_procedure,3,70,Procedure/Post
  Oracle_Postgres14,Offset,*offset* & rows,No Predecessor,3,80,Procedure/Statement/Pre
  ```
- **Module Path Construction**: `Conversion_Modules/{migration_name}/{object_path}/{feature_name}.py`

#### Module Update Process:
- **Step 1**: Read CSV file from `Conversion_Modules/{migration_name}/{migration_name}.csv`
- **Step 2**: Lookup `Object_Path` for each responsible feature from CSV using `Feature_Name`
- **Step 3**: Construct full path: `Conversion_Modules/{migration_name}/{object_path}/{feature_name}.py`
- **Step 4**: Read Python files from constructed paths
- **Example**: For feature `nvl` → `Conversion_Modules/Oracle_Postgres14/Common/Statement/Pre/nvl.py`
- **AI Module Update**:
  - Analyze `after_statement_level` target conversion requirements
  - Compare `source_statement` vs `ai_converted_statement` with similar variations
  - Update Python modules to handle statement conversion with flexibility for variations
  - Consider multiple conversion patterns and edge cases
  - Generate modified code for each responsible module that can handle similar statement variations

#### Output Format:
```
[
  (feature_name, original_python_code, modified_python_code, object_path, modified_flag),
  (feature_name2, original_python_code2, modified_python_code2, object_path2, modified_flag)
]
```

### 10. Code Quality Valid Decision
- **AI Code Validation**: Validate modified Python modules for syntax, logic, and conversion requirements
- **Success Actions**:
  - **Insert into `stage2_conversion_modules`**:
    ```sql
    INSERT INTO stage2_conversion_modules (
      statement_id, Feature_Name, Object_Path, Attempt, 
      Original_module, updated_module, created_dt
    )
    ```
  - Store `id` for future updates
- **Failure**: Return to Update Responsible Modules

### 11. Apply Updated Modules

#### Enhanced Module Application Sequence:
```
after_type_casting_statement
→ apply statement_level features (apply all available feature modules + updated modules)
→ apply post procedure features
→ updated_AI_Converted_Statement
```

#### Module Application Logic:
- **Start with**: `after_type_casting_statement`
- **Apply statement-level features**:
  - Apply **all available feature modules** from the system
  - Use **modified code** if `modified_flag=true` for updated modules
  - Use **original modules** for non-updated features
  - Apply based on `object_path` mapping
- **Apply post-procedure features**
- **Generate**: `updated_AI_Converted_Statement`

### 12. Test Modules
- **Module Functionality Test**: Test updated modules in QMigrator environment
- **Validation**: Verify modules execute without errors and conversion logic works correctly
- **Success**: Proceed to AI comparison
- **Failure**: Return to Update Responsible Modules

### 13. Compare AI Statements

#### Enhanced AI Comparison:
- **Compare**: `ai_converted_statement` (original) vs `updated_AI_Converted_Statement` (new)
- **AI determines**: Are statements functionally equivalent and same?
- **Account for**: Acceptable variations in formatting/syntax
- **Match**: Move to next statement processing
- **No Match**:
  - Increment attempt counter
  - If < 5 attempts: Return to Update Responsible Modules
  - If = 5 attempts: Use latest attempt (Attempt 5) as the correct one and move to next statement

### 14. More Statements Decision
- **Statement Processing Loop**:
  - Check if more statements exist in the approved statements list
  - **Yes**: Return to Identify Responsible Features for next statement
  - **No**: Complete processing

### 15. Complete Processing
- **Final Actions**:
  - Update `stage2_conversion_modules` for successful modules
  - Generate processing summary report
  - Clean up temporary resources
  - Mark workflow as completed

## Database Tables

### stage2_statements Table:
```sql
CREATE TABLE IF NOT EXISTS public.stage2_statements (
    statement_id BIGSERIAL PRIMARY KEY,
    process_type TEXT,
    tgt_object_id BIGINT,
    schema_name VARCHAR(50),
    tgt_object_name VARCHAR(100),
    migration_name VARCHAR(100),
    original_error TEXT,
    original_source_statement TEXT,
    source_before_statement_level TEXT,
    source_after_statement_level TEXT,
    target_statement TEXT,
    ai_converted_statement TEXT,
    statement_level_features TEXT,
    program_level_features TEXT,
    responsible_statement_level_features TEXT,
    responsible_program_level_features TEXT,
    created_dt TIMESTAMP DEFAULT NOW(),
    updated_dt TIMESTAMP NULL
);
```

### stage2_conversion_modules Table:
```sql
CREATE TABLE IF NOT EXISTS public.stage2_conversion_modules (
    id BIGSERIAL PRIMARY KEY,
    statement_id BIGINT NOT NULL,
    Feature_Name VARCHAR,
    Object_Path TEXT,
    Attempt INT,
    Original_module TEXT,
    updated_module TEXT,
    is_approved BOOLEAN,
    created_dt TIMESTAMP DEFAULT NOW(),
    updated_dt TIMESTAMP NULL,
    CONSTRAINT fk_statement
        FOREIGN KEY (statement_id)
        REFERENCES public.stage2_statements(statement_id)
        ON DELETE CASCADE
);
```

## Stored Procedures

### Insert Stage2 Statement
```sql
CREATE OR REPLACE FUNCTION insert_stage2_statement(
    p_process_type TEXT,
    p_tgt_object_id BIGINT,
    p_schema_name VARCHAR(50),
    p_tgt_object_name VARCHAR(100),
    p_migration_name VARCHAR(100),
    p_original_error TEXT,
    p_original_source_statement TEXT,
    p_source_before_statement_level TEXT,
    p_source_after_statement_level TEXT,
    p_target_statement TEXT,
    p_ai_converted_statement TEXT,
    p_statement_level_features TEXT,
    p_program_level_features TEXT
) RETURNS BIGINT AS $$
DECLARE
    new_statement_id BIGINT;
BEGIN
    INSERT INTO stage2_statements (
        process_type, tgt_object_id, schema_name, tgt_object_name,
        migration_name, original_error, original_source_statement,
        source_before_statement_level, source_after_statement_level,
        target_statement, ai_converted_statement, statement_level_features,
        program_level_features, created_dt
    ) VALUES (
        p_process_type, p_tgt_object_id, p_schema_name, p_tgt_object_name,
        p_migration_name, p_original_error, p_original_source_statement,
        p_source_before_statement_level, p_source_after_statement_level,
        p_target_statement, p_ai_converted_statement, p_statement_level_features,
        p_program_level_features, NOW()
    ) RETURNING statement_id INTO new_statement_id;

    RETURN new_statement_id;
END;
$$ LANGUAGE plpgsql;
```

### Update Responsible Features
```sql
CREATE OR REPLACE FUNCTION update_responsible_features(
    p_statement_id BIGINT,
    p_responsible_statement_level_features TEXT,
    p_responsible_program_level_features TEXT
) RETURNS VOID AS $$
BEGIN
    UPDATE stage2_statements SET
        responsible_statement_level_features = p_responsible_statement_level_features,
        responsible_program_level_features = p_responsible_program_level_features,
        updated_dt = NOW()
    WHERE statement_id = p_statement_id;
END;
$$ LANGUAGE plpgsql;
```

### Insert Conversion Module
```sql
CREATE OR REPLACE FUNCTION insert_conversion_module(
    p_statement_id BIGINT,
    p_feature_name VARCHAR,
    p_object_path TEXT,
    p_attempt INT,
    p_original_module TEXT,
    p_updated_module TEXT
) RETURNS BIGINT AS $$
DECLARE
    new_module_id BIGINT;
BEGIN
    INSERT INTO stage2_conversion_modules (
        statement_id, Feature_Name, Object_Path, Attempt,
        Original_module, updated_module, created_dt
    ) VALUES (
        p_statement_id, p_feature_name, p_object_path, p_attempt,
        p_original_module, p_updated_module, NOW()
    ) RETURNING id INTO new_module_id;

    RETURN new_module_id;
END;
$$ LANGUAGE plpgsql;
```

## Key Enhancements

### 1. Stage 1 Metadata Integration:
- Added `migration_name` and `objecttype` as input parameters
- File paths now use Stage 1 metadata structure: `Stage1_Metadata/migration_name/schema_name/objecttype/object_name`
- CSV files generated from Stage 1 with approved statements and metadata
- Conversion_Files.xlsx available at schema level: `Stage1_Metadata/migration_name/schema_name/`
- Pattern-based file copying: All files ending with `_Package_Extraction.csv` copied to schema level
- Module paths reference migration-specific files from Stage 1 metadata generation

### 2. Enhanced AI Mapping:
- **Smart statement matching** between approved (5) and object-level (50) statements
- **AI-driven similarity detection** beyond just statement numbers
- **Robust validation** ensuring correct feature mapping

### 3. Detailed Module Management:
- **Conversion_Modules folder structure**: `Conversion_Modules/{migration_name}/{object_path}/{feature_name}.py`
- **CSV-based path resolution**: Feature paths stored in `Conversion_Modules/{migration_name}/{migration_name}.csv`
- **Migration-specific paths**: Each migration has its own module folder structure and CSV mapping
- **Real example**: `Conversion_Modules/Oracle_Postgres14/Common/Statement/Pre/nvl.py`
- **Mixed module application**: Modified modules + original QMigrator modules
- **Path-based module resolution**: Using `migration_name` and CSV lookup for `Object_Path` mapping

### 4. Improved Error Handling:
- **5-attempt limit per statement** with latest attempt (Attempt 5) considered as correct
- **Granular retry mechanisms** at each validation point
- **Comprehensive validation loops** ensuring quality at each step
- **Latest attempt acceptance** when max attempts reached

## Retry Mechanisms

1. **Feature Mapping** → Available Features Validation failure
2. **Responsible Features** → Features Valid failure
3. **Module Updates** → Code Quality Valid failure
4. **Module Updates** → Test Modules failure
5. **Statement Comparison** → 5 attempt limit per statement (Attempt 5 accepted as correct)

## Manual Review Interface (Separate UI Application)

### Frontend React Application Structure
The manual review interface is a separate UI application that uses the Stage 2 workflow data:

#### Selection Criteria:
- **Process Type**: qmigrator or qbook
- **Migration Context**: migration_name selection
- **Schema Object**: schema_name/objecttype/object_name selection
- **Statement List**: Display all statements for selected object from Stage 1 metadata

#### Statement Management Structure:
```
Statement 1
├── Attempt 1 (Interactive) ⬇️
│   ├── 📁 xml (Feature 1)
│   │   ├── Object Path: Common/Statement/Pre
│   │   ├── Original Code: [Collapsible code block]
│   │   ├── Modified Code: [Collapsible code block]
│   │   └── Actions: Save/Approve/Reject
│   ├── 📁 nvl (Feature 2)
│   │   ├── Object Path: Common/Statement/Pre
│   │   ├── Original Code: [Collapsible code block]
│   │   ├── Modified Code: [Collapsible code block]
│   │   └── Actions: Save/Approve/Reject
│   ├── 📁 sysdate (Feature 3)
│   │   └── [Similar structure]
│   └── 🔄 Merge All Approved (Button)
├── Attempt 2 (Reference Only) ⬇️
│   ├── 📁 xml (Feature 1) - Read Only
│   ├── 📁 decode (Feature 2) - Read Only
│   └── 📁 to_char (Feature 3) - Read Only
├── Attempt 3 (Reference Only) ⬇️
│   └── [Multiple modules - Display only for reference]
├── Attempt 4 (Reference Only) ⬇️
│   └── [Multiple modules - Display only for reference]
├── Attempt 5 (Reference Only - Latest/Correct) ⬇️
│   └── [Multiple modules - Display only - considered as correct when max attempts reached]
└── 🔄 Merge Final (Upload to QBook site - Available for Attempt 5)

Statement 2, 3, 4, 5...
└── [Similar structure]
```

#### Action Definitions:
- **Save**: Store changes to database
- **Approve**: Mark module as approved for production use
- **Reject**: Mark module as rejected, requiring rework
- **Merge**: Upload approved module to QBook site (only for Attempt 1)
- **Attempts 2-5**: Display only for reference, no interactive actions

#### Data Retrieval Queries:
```sql
-- Get statements by process type and object
SELECT * FROM stage2_statements
WHERE process_type = ? AND schema_name = ? AND tgt_object_name = ?
ORDER BY statement_id;

-- Get conversion modules by statement
SELECT * FROM stage2_conversion_modules
WHERE statement_id = ?
ORDER BY Attempt, Feature_Name;
```

#### Key Features:
- **Attempt 1**: Full interactive capabilities with Save/Approve/Reject/Merge
- **Attempts 2-5**: Read-only display for reference and learning
- **Attempt 5 Special**: Considered as correct when max attempts reached
- **Merge Functionality**: Available for approved Attempt 1 modules or Attempt 5 (when max attempts reached)
- **QBook Integration**: Direct upload capability for approved modules

This enhanced workflow provides robust processing with detailed AI validation, comprehensive module management, and migration-specific context handling. The separate manual review interface allows human oversight and approval of AI-generated module updates.

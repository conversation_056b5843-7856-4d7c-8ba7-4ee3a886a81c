Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Mysql_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (949)
Mysql_Mysql,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;&Start: Create view End:;,,,View,ObjectTypes object (964)
Mysql_Mysql,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (934)
Mysql_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (952)
Mysql_Mysql,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1&Start: Create Definer End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (967)
Mysql_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (955)
Mysql_Mysql,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1&Start: Create Definer End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (970)
Mysql_Mysql,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (940)
Mysql_Mysql,Start:create table End:object-1&Start:create table End:;,,,Partition,ObjectTypes object (943)
Mysql_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (958)
Mysql_Mysql,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (973)
Mysql_Mysql,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (946)
Mysql_Mysql,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (961)
Mysql_Mysql,Start: Create Event End:Object-1,Start:/* End:*/&Start:-- End:\n,,Event,ObjectTypes object (976)

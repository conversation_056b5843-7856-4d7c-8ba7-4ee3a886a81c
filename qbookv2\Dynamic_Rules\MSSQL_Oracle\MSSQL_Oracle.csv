Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
MSSQL_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (232)
MSSQL_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (226)
MSSQL_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (229)
MSSQL_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (220)
MSSQL_Oracle,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (235)
MSSQL_Oracle,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (223)
MSSQL_Oracle,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (238)
MSSQL_Oracle,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (214)
MSSQL_Oracle,Start:create sequence End:object-1&Start:create sequence End:;,,,Sequence,ObjectTypes object (217)
MSSQL_Oracle,Start: Create or replace force view End:;&Start: Create view End:;&Start: Create or replace force view End:Object-1,,,View,ObjectTypes object (631)

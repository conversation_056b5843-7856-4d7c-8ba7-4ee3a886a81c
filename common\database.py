import psycopg2


def connect_database(db_data):
    db_data['host'] = '***********'
    connection = psycopg2.connect(user=db_data['name'], password=db_data['password'],
                                  host=db_data['host'], database=db_data['db_name'],
                                  port=db_data['port'])
    return connection


# CREATE TABLE public.tgt_statements (
# 	id bigserial NOT NULL,
# 	tgt_id int8 NOT NULL,
# 	statement_number int8 NOT NULL,
# 	target_statement text NULL,
# 	converted_statement text NULL,
# 	source_statement text NULL,
# 	created_dt timestamp DEFAULT now() NULL,
# 	updated_dt timestamp NULL,
# 	CONSTRAINT tgt_statements_pkey PRIMARY KEY (id),
# 	CONSTRAINT tgt_statements_unique UNIQUE (tgt_id, statement_number)
# );


# CREATE TABLE public.conversion_agent_deployment (
# 	deployment_id bigserial NOT NULL,
# 	tgt_statements_id int8 NOT NULL,
# 	attempt int8 NOT NULL,
# 	source_statement text NULL,
# 	target_statement text NULL,
# 	converted_statement text NULL,
# 	observations text NULL,
# 	is_deployed bool DEFAULT false NULL,
# 	error text NULL,
# 	review_status bool DEFAULT false NULL,
# 	reviewer_comments text NULL,
# 	reviewer_name text NULL,
# 	created_dt timestamp DEFAULT now() NULL,
# 	updated_dt timestamp NULL,
# 	CONSTRAINT conversion_agent_deployment_pkey PRIMARY KEY (deployment_id),
# 	CONSTRAINT deployment_unique UNIQUE (tgt_statements_id, attempt)
# );


# -- public.conversion_agent_deployment foreign keys

# ALTER TABLE public.conversion_agent_deployment ADD CONSTRAINT fk_tgt_statements FOREIGN KEY (tgt_statements_id) REFERENCES public.tgt_statements(id) ON DELETE CASCADE;

# INSERT INTO public.tgt_statements (tgt_id, statement_number,target_statement,
#     converted_statement,
#     source_statement,
#     created_dt
# )
# VALUES (
# tgt_id,
#     statement_number,
#     target_statement,
#     converted_statement,
#     source_statement,
   
#     now()
# )
# ON CONFLICT (tgt_id, statement_number)
# DO UPDATE SET
#     target_statement = EXCLUDED.target_statement,
#     converted_statement = EXCLUDED.converted_statement,
#     source_statement = EXCLUDED.source_statement,
#     updated_dt = now();

# public.sp_tgt_statements_insert(IN p_tgt_id bigint, IN p_statement_number bigint, IN p_target_statement text, IN p_converted_statement text, IN p_source_statement text, INOUT ref refcursor)
# public.sp_conversion_agent_deployment_insert(IN p_tgt_id bigint,p_statement_number bigint, IN p_attempt bigint,IN p_source_statement text, IN p_target_statement text, IN p_converted_statement text,  IN p_observations text, IN p_is_deployed boolean, IN p_error text, INOUT ref refcursor)



def target_statements_batch_insert(connection, tgt_id, statements_data):
    """
    Batch insert function for target statements using direct INSERT with ON CONFLICT.

    Args:
        connection: Database connection object
        tgt_id: Target ID
        statements_data: List of tuples containing (statement_number, target_statement)

    Returns:
        bool: True if successful, False otherwise
    """
    cursor = connection.cursor()
    success = True

    try:
        # First, delete existing records for this tgt_id
        delete_sql = "DELETE FROM public.tgt_statements WHERE tgt_id = %s"
        cursor.execute(delete_sql, (int(tgt_id),))
        print(f'Deleted existing records for tgt_id: {tgt_id}')

        # Prepare batch data for direct INSERT
        batch_data = []
        for statement_number, target_statement in statements_data:
            batch_data.append((int(tgt_id), int(statement_number), target_statement, None, None))

        # Direct INSERT statement (no ON CONFLICT needed since we deleted first)
        insert_sql = """
        INSERT INTO public.tgt_statements (
            tgt_id,
            statement_number,
            target_statement,
            converted_statement,
            source_statement,
            created_dt
        )
        VALUES (%s, %s, %s, %s, %s, now())
        """
        
        cursor.executemany(insert_sql, batch_data)
        connection.commit()
        print(f'Target statements batch insert executed successfully - {len(statements_data)} records processed')

    except Exception as error:
        success = False
        connection.rollback()
        print("Error at target statements batch insert: ", str(error))
    finally:
        cursor.close()
    return success


def conversion_agent_deployment_insert(connection, tgt_id, statement_number, source_statement_number, attempt, source_statement, target_statement, converted_statement, observations, is_deployed, error):
    """
    Insert conversion agent deployment record using stored procedure.

    Args:
        connection: Database connection object
        tgt_id: Target ID
        statement_number: Statement number
        attempt: Attempt number
        source_statement: Source statement text
        source_statement_number: Source statement number
        target_statement: Target statement text
        converted_statement: Converted statement text
        observations: Observations text
        is_deployed: Boolean indicating if deployed
        error: Error text

    Returns:
        bool: True if successful, False otherwise
    """
    cursor = connection.cursor()
    success = True

    try:
        # Call stored procedure for conversion agent deployment insert
        sp_call = 'call public.sp_conversion_agent_deployment_insert(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s);fetch all in "refcursor"'
        # print(sp_call, (int(tgt_id), int(statement_number), int(attempt), source_statement, target_statement, converted_statement, observations, is_deployed, error, 'refcursor'), 'conversion_agent_deployment_insert')
        cursor.execute(sp_call, (int(tgt_id), int(statement_number), int(attempt), source_statement, source_statement_number, target_statement, converted_statement, observations, is_deployed, error, 'refcursor'))
        connection.commit()
        print(f'Conversion agent deployment insert executed successfully - tgt_id: {tgt_id}, statement_number: {statement_number}, attempt: {attempt}')

    except Exception as error:
        success = False
        connection.rollback()
        print("Error at conversion agent deployment insert: ", str(error))
    finally:
        cursor.close()
    return success


def request_insert(connection, iteration_id, connection_id, operation_name, operation_category, schema_name,
                   object_type):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.request_insert(%s,%s,%s,%s,%s,%s,%s);fetch all in "dataset"',
                       (iteration_id, connection_id, operation_name, operation_category, schema_name, object_type,
                        'dataset'))
        data = cursor.fetchall()
        print("Request insert executed successfully")
    except Exception as error:
        data = None
        print("Error at request insert: ", str(error))
    finally:
        connection.commit()
        cursor.close()
    return data


def request_update(connection, request_id, status, error):
    cursor = connection.cursor()
    try:
        cursor.execute('call public.request_update(%s,%s,%s,%s);fetch all in "dataset";',
                       (request_id, status, error, 'dataset'))
        print("Request update executed successfully")
    except Exception as err:
        print("Error at request update: ", str(err))
    finally:
        connection.commit()
        cursor.close()


def get_approved_statements_from_db(connection, tgt_object_id):
    """
    Retrieve approved statements from database where review_status and is_manually_deployed are both true.

    Args:
        tgt_object_id (int): The target object ID

    Returns:
        list: List of approved statements data
    """
    try:
        cursor = connection.cursor()

        query = """
        SELECT
            cad.source_statement_number,
            ts.statement_number as target_statement_id,
            cad.source_statement,
            cad.target_statement,
            cad.converted_statement as ai_converted_statement
        FROM public.conversion_agent_deployment cad
        INNER JOIN public.tgt_statements ts ON cad.tgt_statements_id = ts.id
        WHERE cad.review_status = true
            AND cad.is_manually_deployed = true
            AND ts.tgt_id = %s
        ORDER BY cad.created_dt
        """

        cursor.execute(query, (tgt_object_id,))
        results = cursor.fetchall()

        approved_statements = []
        for result in results:
            approved_statements.append({
                'source_statement_number': result[0],
                'target_statement_id': result[1],
                'source_statement': result[2],
                'target_statement': result[3],
                'ai_converted_statement': result[4]
            })

        print(f"📊 Retrieved {len(approved_statements)} approved statements from database for tgt_object_id: {tgt_object_id}")
        return approved_statements

    except Exception as e:
        print(f"❌ Error retrieving approved statements: {str(e)}")
        return []
    finally:
        if 'cursor' in locals():
            cursor.close()
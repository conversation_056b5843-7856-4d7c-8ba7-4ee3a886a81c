Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Sybase_SQL,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (730)
Sybase_SQL,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Unique_Index,ObjectTypes object (736)
Sybase_SQL,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (733)
Sybase_SQL,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (700)
Sybase_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (739)
Sybase_SQL,Start: Create view End:Object-1&Start: Create view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (697)
Sybase_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (706)
Sybase_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (742)
Sybase_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (715)
Sybase_SQL,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (745)
